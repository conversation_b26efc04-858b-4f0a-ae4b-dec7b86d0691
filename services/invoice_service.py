import os
import base64
import time
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from PyPDF2 import PdfReader, PdfWriter

class InvoiceService:
    """Service for generating and managing invoices."""

    def __init__(self, driver, supabase_client):
        self.driver = driver
        self.supabase = supabase_client

    def download_invoice(self, auction_id, contact_url, plan_id):
        """Download invoice for a specific auction."""
        # Create directory if it doesn't exist
        os.makedirs(f"invoices/{plan_id}", exist_ok=True)
        output_pdf_path = f"invoices/{plan_id}/{auction_id}.pdf"

        # Open the contact URL
        self.driver.get(contact_url)
        time.sleep(1)

        # Find payment details link
        try:
            payment_detail_elements = self.driver.find_elements(By.XPATH, "//a[contains(text(),'支払い明細')]")
            payment_detail_element = payment_detail_elements[0] if payment_detail_elements else None

            if payment_detail_element:
                payment_detail_url = payment_detail_element.get_attribute("href")
                self.driver.get(payment_detail_url)
                # Wait for page to load
                WebDriverWait(self.driver, 20).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            elif "buy.auctions" in contact_url:
                self.driver.get(contact_url.replace("status?", "receipt?"))
                time.sleep(2)
            else:
                print(f"No payment details link found for auction {auction_id}")
                return None
        except Exception as e:
            print(f"Error finding payment details link: {e}")
            return None

        # Use Chrome DevTools Protocol to print page to PDF
        result = self.driver.execute_cdp_cmd("Page.printToPDF", {"printBackground": True})

        # Check result
        if 'data' not in result or not result['data']:
            print(f"Failed to generate PDF for auction {auction_id}")
            return None

        # Extract PDF data and save to file
        pdf_data = base64.b64decode(result['data'])
        with open(output_pdf_path, 'wb') as file:
            file.write(pdf_data)

        print(f"Invoice saved to {output_pdf_path}")
        return output_pdf_path

    def download_all_invoices(self, plan_id):
        """Download invoices for all finished auctions in a plan."""
        # Get all finished auctions for the plan
        auctions = self.supabase.table("auctions").select("id,plan_id,contact_url").eq("plan_id", plan_id).eq("status", "finished").execute().data

        print(f"Found {len(auctions)} finished auctions for plan {plan_id}")

        # Download invoices for each auction
        downloaded_invoices = []
        for auction in auctions:
            auction_id = auction["id"]
            contact_url = auction["contact_url"]

            # Check if invoice already exists
            output_pdf_path = f"invoices/{plan_id}/{auction_id}.pdf"
            if os.path.exists(output_pdf_path):
                downloaded_invoices.append(output_pdf_path)
                continue

            # Download invoice
            print(f"Downloading invoice for auction {auction_id}")
            invoice_path = self.download_invoice(auction_id, contact_url, plan_id)
            if invoice_path:
                downloaded_invoices.append(invoice_path)

        return downloaded_invoices

    def process_invoices(self, plan_id):
        """Process invoices by keeping only the first page and merging them."""
        # Create directories if they don't exist
        input_directory = f"invoices/{plan_id}"
        output_directory = "invoices/processed"
        os.makedirs(output_directory, exist_ok=True)

        # Process each invoice
        for file_name in os.listdir(input_directory):
            if file_name.endswith(".pdf") and not file_name.startswith("Invoices_"):
                input_path = os.path.join(input_directory, file_name)
                output_path = os.path.join(output_directory, file_name)
                self.keep_first_page(input_path, output_path)

        # Merge processed invoices
        output_pdf_path = f"invoices/{plan_id}/Invoices_{plan_id}.pdf"
        self.merge_pdfs(output_directory, output_pdf_path)

        return output_pdf_path

    def keep_first_page(self, input_pdf_path, output_pdf_path):
        """Keep only the first page of a PDF file."""
        try:
            # Read input PDF
            reader = PdfReader(input_pdf_path)

            # Check number of pages
            if len(reader.pages) >= 2:
                # Write only the first page
                writer = PdfWriter()
                writer.add_page(reader.pages[0])

                # Save output PDF
                with open(output_pdf_path, "wb") as output_file:
                    writer.write(output_file)
            else:
                # Copy file
                os.system(f"cp {input_pdf_path} {output_pdf_path}")
        except Exception as e:
            print(f"Error processing {input_pdf_path}: {e}")

    def merge_pdfs(self, input_directory, output_pdf_path):
        """Merge all PDFs in a directory into a single PDF."""
        try:
            writer = PdfWriter()

            # Process each PDF in the directory
            for file_name in sorted(os.listdir(input_directory)):
                if file_name.endswith(".pdf"):
                    input_pdf_path = os.path.join(input_directory, file_name)

                    # Read PDF
                    reader = PdfReader(input_pdf_path)

                    # Add all pages to output
                    for page in reader.pages:
                        writer.add_page(page)

            # Save output PDF
            with open(output_pdf_path, "wb") as output_file:
                writer.write(output_file)

            print(f"Merged PDF saved to {output_pdf_path}")
            return output_pdf_path
        except Exception as e:
            print(f"Error merging PDFs: {e}")
            return None

    def calculate_total_cost(self, plan_ids=None):
        """Calculate total cost for specified plans."""
        if plan_ids is None:
            plan_ids = []  # Default plan IDs

        total = 0
        tracking_numbers = []
        log_messages = []

        try:
            # Log the plans being processed
            log_messages.append(f"Calculating total cost for plans: {', '.join(map(str, plan_ids))}")

            # Get auctions and additional expenses for the plans
            all_auctions = self.supabase.from_('auctions').select().in_('plan_id', plan_ids).execute().data
            log_messages.append(f"Found {len(all_auctions)} auctions for selected plans")

            additional_expenses = self.supabase.from_('additional_expenses').select().in_('plan_id', plan_ids).execute().data
            log_messages.append(f"Found {len(additional_expenses)} additional expenses for selected plans")

            # Process each auction
            auction_costs = []
            for auction in all_auctions:
                auction_id = auction.get('id', 'unknown')
                auction_total = 0
                auction_log = []

                # If payment_total has a value
                if auction.get('payment_total'):
                    payment_total = auction['payment_total']

                    # If tracking_number has a value, check for duplicates
                    if auction.get('tracking_number'):
                        tracking_number = auction['tracking_number']
                        if tracking_number not in tracking_numbers:
                            auction_total += payment_total
                            tracking_numbers.append(tracking_number)
                            auction_log.append(f"Payment total: ¥{payment_total:,}")
                            auction_log.append(f"Unique tracking number: {tracking_number}")
                        else:
                            auction_log.append(f"Duplicate tracking number: {tracking_number} (not counted twice)")
                    else:
                        # If tracking_number is null or empty, still count in total
                        auction_total += payment_total
                        auction_log.append(f"Payment total: ¥{payment_total:,}")
                        auction_log.append("No tracking number")
                else:
                    # Use price as fallback
                    price = auction.get('price', 0)
                    if price:
                        auction_total += price
                        auction_log.append(f"Using price as fallback: ¥{price:,}")
                    else:
                        auction_log.append("No payment total or price available")

                # Add COD (Cash On Delivery) costs if any
                cod = auction.get('cod', 0) or 0  # Handle None value
                if cod:
                    auction_total += cod
                    auction_log.append(f"Added COD cost: ¥{cod:,}")

                # Add to total
                if auction_total > 0:
                    total += auction_total
                    auction_costs.append({
                        'id': auction_id,
                        'name': auction.get('name', 'Unknown'),
                        'total': auction_total,
                        'log': auction_log
                    })

            # Add additional expenses
            expense_total = sum([expense.get('amount', 0) or 0 for expense in additional_expenses])
            if expense_total > 0:
                total += expense_total
                log_messages.append(f"Added additional expenses: ¥{expense_total:,}")

            # Log detailed breakdown if requested
            if len(auction_costs) > 0:
                log_messages.append("\nAuction cost breakdown:")
                for i, cost in enumerate(auction_costs, 1):
                    log_messages.append(f"\n{i}. Auction {cost['id']}: {cost['name']}")
                    log_messages.append(f"   Total: ¥{cost['total']:,}")
                    for log_line in cost['log']:
                        log_messages.append(f"   - {log_line}")

            # Calculate balance and VND amount
            exchange_data = self.get_exchange_data()
            total_vnd = 0
            balance_jpy = 0
            balance_vnd = 0

            if exchange_data:
                # Print raw exchange data for debugging
                print("\nExchange data:")
                for i, ex in enumerate(exchange_data):
                    print(f"Exchange {i+1}: JPY={ex.get('jpy', 0)}, VND={ex.get('vnd', 0)}")

                total_jpy_exchanged = sum([ex.get('jpy', 0) or 0 for ex in exchange_data])
                total_vnd_exchanged = sum([ex.get('vnd', 0) or 0 for ex in exchange_data])

                # Print intermediate values for debugging
                print(f"Total JPY exchanged: {total_jpy_exchanged}")
                print(f"Total cost: {total}")

                # Calculate balance
                balance_jpy = total_jpy_exchanged - total
                balance_vnd = total_vnd_exchanged

                # Print balance for debugging
                print(f"Balance JPY: {balance_jpy}")

                log_messages.append(f"\nExchange Information:")
                log_messages.append(f"Total JPY exchanged: ¥{total_jpy_exchanged:,}")
                log_messages.append(f"Total VND exchanged: {total_vnd_exchanged:,.0f} VND")
                log_messages.append(f"Total cost: ¥{total:,}")
                log_messages.append(f"Balance JPY: ¥{balance_jpy:,}")
                log_messages.append(f"Balance VND: {balance_vnd:,.0f} VND")

            return {
                'total': total,
                'tracking_numbers': tracking_numbers,
                'auction_count': len(all_auctions),
                'expense_count': len(additional_expenses),
                'balance_jpy': balance_jpy,
                'balance_vnd': balance_vnd,
                'log_messages': log_messages
            }

        except Exception as e:
            import traceback
            error_message = f"Error calculating total cost: {str(e)}\n{traceback.format_exc()}"
            print(error_message)
            return {
                'total': 0,
                'tracking_numbers': [],
                'auction_count': 0,
                'expense_count': 0,
                'balance_jpy': 0,
                'balance_vnd': 0,
                'error': error_message,
                'log_messages': log_messages
            }

    def get_exchange_data(self):
        """Get all exchange data from the database."""
        try:
            # Get all exchange data
            exchange_data = self.supabase.from_('exchanges').select('*').execute().data
            return exchange_data
        except Exception as e:
            import traceback
            print(f"Error getting exchange data: {str(e)}\n{traceback.format_exc()}")
            return []

    def get_exchange_rate(self, currency="VND"):
        """Get the latest exchange rate from the database."""
        try:
            # Get all exchange data
            exchange_data = self.get_exchange_data()

            if exchange_data and len(exchange_data) > 0:
                # Calculate total JPY and VND
                total_jpy = sum([ex.get('jpy', 0) or 0 for ex in exchange_data])
                total_vnd = sum([ex.get('vnd', 0) or 0 for ex in exchange_data])

                # Calculate average exchange rate
                if total_jpy > 0:
                    return total_vnd / total_jpy

            # Default fallback rate
            return 170  # Approximate VND to JPY rate
        except Exception as e:
            import traceback
            print(f"Error getting exchange rate: {str(e)}\n{traceback.format_exc()}")
            return 170  # Default fallback rate

    def calculate_total_in_currency(self, total_jpy, currency="VND"):
        """Calculate total in a different currency."""
        try:
            # Get all exchange data
            exchange_data = self.get_exchange_data()

            if exchange_data and len(exchange_data) > 0:
                # Calculate total JPY and VND
                total_jpy_exchanged = sum([ex.get('jpy', 0) or 0 for ex in exchange_data])
                total_vnd_exchanged = sum([ex.get('vnd', 0) or 0 for ex in exchange_data])

                # Calculate average exchange rate
                if total_jpy_exchanged > 0:
                    exchange_rate = total_vnd_exchanged / total_jpy_exchanged
                    return total_jpy * exchange_rate

            # Use default rate as fallback
            if currency.upper() == "VND":
                return total_jpy * 170  # Default VND rate
            return 0
        except Exception as e:
            import traceback
            print(f"Error calculating total in currency: {str(e)}\n{traceback.format_exc()}")
            # Use default rate as fallback
            if currency.upper() == "VND":
                return total_jpy * 170  # Default VND rate
            return 0
